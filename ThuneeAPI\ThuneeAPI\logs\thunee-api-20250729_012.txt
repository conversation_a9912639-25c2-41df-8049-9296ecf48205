2025-07-29 13:04:33.152 +02:00 [INF] Starting Thunee API Server
2025-07-29 13:29:47.961 +02:00 [INF] Starting Thunee API Server
2025-07-29 13:30:27.031 +02:00 [INF] Attempting to advance competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32
2025-07-29 13:30:38.602 +02:00 [INF] Advancing competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32
2025-07-29 13:31:13.681 +02:00 [INF] Getting eligible teams for next phase in competition "4860c19d-e3f3-4e2d-b359-275527461bd6"
2025-07-29 13:33:18.169 +02:00 [INF] Found 52 teams in phase Leaderboard for competition "4860c19d-e3f3-4e2d-b359-275527461bd6"
2025-07-29 13:36:22.339 +02:00 [INF] Returning 0 eligible teams for advancement to Leaderboard
2025-07-29 13:36:22.361 +02:00 [INF] Found 0 eligible teams for phase Top32
2025-07-29 13:36:22.372 +02:00 [INF] Executing SP_AdvanceCompetitionPhase for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32 with 0 teams
2025-07-29 13:36:22.513 +02:00 [ERR] Error advancing competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32
System.InvalidOperationException: Stored procedure 'SP_AdvanceCompetitionPhase' not found in resources
   at ThuneeAPI.Infrastructure.Helpers.StoredProcedureHelper.GetStoredProcedure(String procedureName) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Helpers\StoredProcedureHelper.cs:line 25
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteStoredProcedureAsync(String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 70
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.ExecuteStoredProcedureAsync(String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 140
   at ThuneeAPI.Infrastructure.Services.CompetitionPhaseService.AdvanceCompetitionPhaseAsync(Guid competitionId, String newPhase) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionPhaseService.cs:line 97
2025-07-29 13:36:22.613 +02:00 [ERR] Error advancing competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32. Error: Stored procedure 'SP_AdvanceCompetitionPhase' not found in resources
System.InvalidOperationException: Stored procedure 'SP_AdvanceCompetitionPhase' not found in resources
   at ThuneeAPI.Infrastructure.Helpers.StoredProcedureHelper.GetStoredProcedure(String procedureName) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Helpers\StoredProcedureHelper.cs:line 25
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteStoredProcedureAsync(String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 70
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.ExecuteStoredProcedureAsync(String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 140
   at ThuneeAPI.Infrastructure.Services.CompetitionPhaseService.AdvanceCompetitionPhaseAsync(Guid competitionId, String newPhase) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionPhaseService.cs:line 97
   at ThuneeAPI.Controllers.CompetitionPhaseController.AdvancePhase(Guid competitionId, AdvancePhaseDto dto) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI\Controllers\CompetitionPhaseController.cs:line 34
