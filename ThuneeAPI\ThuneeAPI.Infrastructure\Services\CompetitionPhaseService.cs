using Microsoft.Extensions.Logging;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;
using ThuneeAPI.Infrastructure.Data.Repositories;

namespace ThuneeAPI.Infrastructure.Services;

// Helper class for stored procedure results
public class CompetitionTeamWithUserInfo
{
    public Guid Id { get; set; }
    public string TeamName { get; set; } = string.Empty;
    public Guid Player1Id { get; set; }
    public Guid? Player2Id { get; set; }
    public string InviteCode { get; set; } = string.Empty;
    public int GamesPlayed { get; set; }
    public int Points { get; set; }
    public int BonusPoints { get; set; }
    public int TotalPoints { get; set; }
    public int MaxGames { get; set; }
    public bool IsActive { get; set; }
    public bool IsComplete { get; set; }
    public DateTime RegisteredAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string Phase { get; set; } = string.Empty;
    public bool IsEliminated { get; set; }
    public bool AdvancedToNextPhase { get; set; }
    public DateTime? PhaseEliminatedAt { get; set; }
    public string? Player1Username { get; set; }
    public string? Player2Username { get; set; }
}

// Static class for SQL command names
public static class SqlCommands
{
    public const string SP_AdvanceCompetitionPhase = "SP_AdvanceCompetitionPhase";
    public const string SP_GetEligibleTeamsForPhase = "SP_GetEligibleTeamsForPhase";
    public const string SP_GetTeamsByPhase = "SP_GetTeamsByPhase";
    public const string SP_CheckTeamEligibilityForGames = "SP_CheckTeamEligibilityForGames";
    public const string SP_CreateCompetitionPhaseLobby = "SP_CreateCompetitionPhaseLobby";
    public const string SP_SetPhaseLobbyWinner = "SP_SetPhaseLobbyWinner";
}

public class CompetitionPhaseService : ICompetitionPhaseService
{
    private readonly ILogger<CompetitionPhaseService> _logger;
    private readonly ICompetitionRepository _competitionRepository;
    private readonly ICompetitionTeamRepository _competitionTeamRepository;
    private readonly IUserRepository _userRepository;
    private readonly IEmailService _emailService;

    public CompetitionPhaseService(
        ILogger<CompetitionPhaseService> logger,
        ICompetitionRepository competitionRepository,
        ICompetitionTeamRepository competitionTeamRepository,
        IUserRepository userRepository,
        IEmailService emailService)
    {
        _logger = logger;
        _competitionRepository = competitionRepository;
        _competitionTeamRepository = competitionTeamRepository;
        _userRepository = userRepository;
        _emailService = emailService;
    }

    public async Task<CompetitionDto> AdvanceCompetitionPhaseAsync(Guid competitionId, string newPhase)
    {
        _logger.LogInformation("Advancing competition {CompetitionId} to phase {Phase}", competitionId, newPhase);

        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        // Get eligible teams for the new phase
        var eligibleTeams = await GetEligibleTeamsForNextPhaseAsync(competitionId);
        var eligibleTeamIds = eligibleTeams.Select(t => t.Id).ToList();

        _logger.LogInformation("Found {Count} eligible teams for phase {Phase}", eligibleTeams.Count, newPhase);

        // Use stored procedure to advance competition phase and manage teams
        var parameters = new
        {
            CompetitionId = competitionId,
            NewPhase = newPhase,
            EligibleTeamIds = string.Join(",", eligibleTeamIds), // Pass as comma-separated string
            PhaseEndDate = GetPhaseEndDate(newPhase),
            MaxGamesPerPhase = GetMaxGamesForPhase(newPhase)
        };

        try
        {
            // For now, let's implement the logic directly until we have the stored procedures set up
            // Update the competition phase
            competition.Phase = newPhase;
            competition.PhaseEndDate = GetPhaseEndDate(newPhase);
            competition.MaxGamesPerPhase = GetMaxGamesForPhase(newPhase);
            competition.UpdatedAt = DateTime.UtcNow;

            _logger.LogInformation("Updating competition {CompetitionId} to phase {Phase} with end date {PhaseEndDate}",
                competitionId, newPhase, competition.PhaseEndDate);

            var affectedRows = await _competitionRepository.UpdateAsync(competition);
            _logger.LogInformation("Competition {CompetitionId} updated successfully. Affected rows: {AffectedRows}", competitionId, affectedRows);

            // Advance eligible teams to the new phase
            _logger.LogInformation("Advancing {Count} teams to phase {Phase}", eligibleTeams.Count, newPhase);
            foreach (var teamDto in eligibleTeams)
            {
                var team = await _competitionTeamRepository.GetByIdAsync(teamDto.Id);
                if (team != null)
                {
                    _logger.LogInformation("Advancing team {TeamId} ({TeamName}) to phase {Phase}", team.Id, team.TeamName, newPhase);
                    team.Phase = newPhase;
                    team.AdvancedToNextPhase = true;
                    var updatedTeam = await _competitionTeamRepository.UpdateAsync(team);
                    _logger.LogInformation("Team {TeamId} advanced successfully to phase {Phase}", team.Id, updatedTeam.Phase);
                }
            }

            // Eliminate teams that are not eligible
            var allTeams = await _competitionTeamRepository.GetByCompetitionIdAsync(competitionId);
            var teamsToEliminate = allTeams.Where(t => !eligibleTeamIds.Contains(t.Id) && !t.IsEliminated);

            _logger.LogInformation("Eliminating {Count} teams", teamsToEliminate.Count());
            foreach (var team in teamsToEliminate)
            {
                _logger.LogInformation("Eliminating team {TeamId} ({TeamName})", team.Id, team.TeamName);
                team.IsEliminated = true;
                team.PhaseEliminatedAt = DateTime.UtcNow;
                var updatedTeam = await _competitionTeamRepository.UpdateAsync(team);
                _logger.LogInformation("Team {TeamId} eliminated successfully. IsEliminated: {IsEliminated}", team.Id, updatedTeam.IsEliminated);
            }

            _logger.LogInformation("Successfully advanced competition {CompetitionId} to phase {Phase} with {EligibleCount} teams advanced and {EliminatedCount} teams eliminated",
                competitionId, newPhase, eligibleTeams.Count, teamsToEliminate.Count());

            return MapToCompetitionDto(competition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error advancing competition {CompetitionId} to phase {Phase}", competitionId, newPhase);
            throw;
        }
    }

    public async Task<List<CompetitionTeamDto>> GetTeamsForPhaseAsync(Guid competitionId, string phase)
    {
        _logger.LogInformation("Getting teams for competition {CompetitionId} phase {Phase}", competitionId, phase);

        try
        {
            var teams = await _competitionTeamRepository.GetByCompetitionIdAsync(competitionId);
            var phaseTeams = teams.Where(t => t.Phase == phase && !t.IsEliminated).ToList();

            var teamDtos = new List<CompetitionTeamDto>();
            foreach (var team in phaseTeams)
            {
                var teamDto = await MapToCompetitionTeamDtoAsync(team);
                teamDtos.Add(teamDto);
            }

            _logger.LogInformation("Found {Count} teams in phase {Phase} for competition {CompetitionId}",
                teamDtos.Count, phase, competitionId);

            return teamDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting teams for phase {Phase} in competition {CompetitionId}", phase, competitionId);
            throw;
        }
    }

    public async Task<List<CompetitionTeamDto>> GetEligibleTeamsForNextPhaseAsync(Guid competitionId)
    {
        _logger.LogInformation("Getting eligible teams for next phase in competition {CompetitionId}", competitionId);

        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        var teams = await _competitionTeamRepository.GetByCompetitionIdAsync(competitionId);

        // Get the previous phase to find teams that should advance to current phase
        var previousPhase = GetPreviousPhase(competition.Phase);
        var eligibleTeams = teams.Where(t => t.Phase == previousPhase && !t.IsEliminated).ToList();

        _logger.LogInformation("Found {Count} teams in phase {Phase} for competition {CompetitionId}",
            eligibleTeams.Count, previousPhase, competitionId);

        // Sort teams by total points (points + bonus points) descending
        var sortedTeams = eligibleTeams
            .Select(MapToCompetitionTeamDto)
            .OrderByDescending(t => t.Points + t.BonusPoints)
            .ThenByDescending(t => t.Points)
            .ThenBy(t => t.TeamName)
            .ToList();

        // Determine how many teams advance based on current phase
        var advancingCount = competition.Phase switch
        {
            "Top32" => 32,  // Top 32 teams from Leaderboard
            "Top16" => 16,  // Top 16 teams from Top32
            "Top8" => 8,    // Top 8 teams from Top16
            "Top4" => 4,    // Top 4 teams from Top8
            "Final" => 2,   // Top 2 teams from Top4
            _ => 0
        };

        var eligibleForAdvancement = sortedTeams.Take(advancingCount).ToList();

        _logger.LogInformation("Returning {Count} eligible teams for advancement to {Phase}",
            eligibleForAdvancement.Count, competition.Phase);

        return eligibleForAdvancement;
    }

    public async Task EliminateTeamsAsync(Guid competitionId, List<Guid> teamIds)
    {
        _logger.LogInformation("Eliminating {Count} teams from competition {CompetitionId}", teamIds.Count, competitionId);
        
        foreach (var teamId in teamIds)
        {
            var team = await _competitionTeamRepository.GetByIdAsync(teamId);
            if (team != null)
            {
                team.IsEliminated = true;
                team.PhaseEliminatedAt = DateTime.UtcNow;
                await _competitionTeamRepository.UpdateAsync(team);
            }
        }
    }

    public async Task AdvanceTeamsToNextPhaseAsync(Guid competitionId, List<Guid> teamIds)
    {
        _logger.LogInformation("Advancing {Count} teams to next phase in competition {CompetitionId}", teamIds.Count, competitionId);
        
        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        var nextPhase = GetNextPhase(competition.Phase);
        
        foreach (var teamId in teamIds)
        {
            var team = await _competitionTeamRepository.GetByIdAsync(teamId);
            if (team != null)
            {
                team.Phase = nextPhase;
                team.AdvancedToNextPhase = true;
                await _competitionTeamRepository.UpdateAsync(team);
            }
        }
    }

    private string GetNextPhase(string currentPhase)
    {
        return currentPhase switch
        {
            "Leaderboard" => "Top32",
            "Top32" => "Top16",
            "Top16" => "Top8",
            "Top8" => "Top4",
            "Top4" => "Final",
            "Final" => "Completed",
            _ => currentPhase
        };
    }

    private string GetPreviousPhase(string currentPhase)
    {
        return currentPhase switch
        {
            "Top32" => "Leaderboard",
            "Top16" => "Top32",
            "Top8" => "Top16",
            "Top4" => "Top8",
            "Final" => "Top4",
            "Completed" => "Final",
            _ => "Leaderboard"
        };
    }

    private DateTime? GetPhaseEndDate(string phase)
    {
        return phase switch
        {
            "Top32" => DateTime.UtcNow.AddDays(7), // 1 week for Top32 leaderboard phase
            "Top16" => DateTime.UtcNow.AddDays(3), // 3 days for knockout phases
            "Top8" => DateTime.UtcNow.AddDays(2),
            "Top4" => DateTime.UtcNow.AddDays(1),
            "Final" => DateTime.UtcNow.AddHours(12),
            _ => null
        };
    }

    private long? GetMaxGamesForPhase(string phase)
    {
        return phase switch
        {
            "Top32" => 10, // Max 10 games in Top32 phase
            "Top16" => 1,  // Single knockout game
            "Top8" => 1,   // Single knockout game
            "Top4" => 1,   // Single knockout game
            "Final" => 1,  // Single knockout game
            _ => null
        };
    }

    private bool IsKnockoutPhase(string phase)
    {
        return phase is "Top32" or "Top16" or "Top8" or "Top4" or "Final";
    }

    // Placeholder implementations for remaining methods
    public async Task<CompetitionPhaseLobbyDto> CreatePhaseLobbyAsync(CreateCompetitionPhaseLobbyDto createDto, Guid adminId)
    {
        throw new NotImplementedException("CreatePhaseLobbyAsync requires full implementation");
    }

    public async Task<List<CompetitionPhaseLobbyDto>> GetPhaseLobbiesAsync(Guid competitionId, string phase)
    {
        throw new NotImplementedException("GetPhaseLobbiesAsync requires full implementation");
    }

    public async Task<CompetitionPhaseLobbyDto> GetPhaseLobbyByCodeAsync(string lobbyCode)
    {
        throw new NotImplementedException("GetPhaseLobbyByCodeAsync requires full implementation");
    }

    public async Task DeletePhaseLobbyAsync(Guid lobbyId)
    {
        throw new NotImplementedException("DeletePhaseLobbyAsync requires full implementation");
    }

    public async Task SetLobbyWinnerAsync(Guid lobbyId, Guid winnerTeamId)
    {
        throw new NotImplementedException("SetLobbyWinnerAsync requires full implementation");
    }

    public async Task<BracketDto> GenerateBracketAsync(Guid competitionId, string phase)
    {
        throw new NotImplementedException("GenerateBracketAsync requires full implementation");
    }

    public async Task<List<BracketDto>> GetCompetitionBracketsAsync(Guid competitionId)
    {
        throw new NotImplementedException("GetCompetitionBracketsAsync requires full implementation");
    }

    public async Task<CompetitionTeamPhaseStatsDto> CreatePhaseStatsAsync(Guid competitionTeamId, string phase)
    {
        throw new NotImplementedException("CreatePhaseStatsAsync requires full implementation");
    }

    public async Task<CompetitionTeamPhaseStatsDto> UpdatePhaseStatsAsync(Guid competitionTeamId, string phase, int points, int bonusPoints, int ballsWon)
    {
        throw new NotImplementedException("UpdatePhaseStatsAsync requires full implementation");
    }

    public async Task<List<CompetitionTeamPhaseStatsDto>> GetTeamPhaseStatsAsync(Guid competitionTeamId)
    {
        throw new NotImplementedException("GetTeamPhaseStatsAsync requires full implementation");
    }

    public async Task ProcessPhaseEndAsync(Guid competitionId)
    {
        throw new NotImplementedException("ProcessPhaseEndAsync requires full implementation");
    }

    public async Task<bool> CanAdvanceToNextPhaseAsync(Guid competitionId)
    {
        throw new NotImplementedException("CanAdvanceToNextPhaseAsync requires full implementation");
    }

    public async Task<List<CompetitionTeamDto>> CalculatePhaseRankingsAsync(Guid competitionId, string phase)
    {
        throw new NotImplementedException("CalculatePhaseRankingsAsync requires full implementation");
    }

    private CompetitionDto MapToCompetitionDto(Competition competition)
    {
        return new CompetitionDto
        {
            Id = competition.Id,
            Name = competition.Name,
            Description = competition.Description,
            StartDate = competition.StartDate,
            EndDate = competition.EndDate,
            Status = competition.Status,
            MaxTeams = competition.MaxTeams,
            EntryFee = competition.EntryFee,
            Phase = competition.Phase,
            PhaseEndDate = competition.PhaseEndDate,
            CreatedAt = competition.CreatedAt
        };
    }

    private async Task<CompetitionTeamDto> MapToCompetitionTeamDtoAsync(CompetitionTeam team)
    {
        var player1 = await _userRepository.GetByIdAsync(team.Player1Id);
        var player2 = team.Player2Id.HasValue ? await _userRepository.GetByIdAsync(team.Player2Id.Value) : null;

        return new CompetitionTeamDto
        {
            Id = team.Id,
            TeamName = team.TeamName,
            Player1 = player1 != null ? new PlayerDto { Id = player1.Id, Username = player1.Username } : new PlayerDto(),
            Player2 = player2 != null ? new PlayerDto { Id = player2.Id, Username = player2.Username } : null,
            InviteCode = team.InviteCode,
            GamesPlayed = team.GamesPlayed,
            Points = team.Points,
            BonusPoints = team.BonusPoints,
            MaxGames = team.MaxGames,
            IsComplete = team.IsComplete,
            RegisteredAt = team.RegisteredAt,
            CompletedAt = team.CompletedAt,
            Phase = team.Phase,
            IsEliminated = team.IsEliminated,
            AdvancedToNextPhase = team.AdvancedToNextPhase,
            PhaseEliminatedAt = team.PhaseEliminatedAt
        };
    }

    private CompetitionTeamDto MapToCompetitionTeamDto(CompetitionTeam team)
    {
        return new CompetitionTeamDto
        {
            Id = team.Id,
            TeamName = team.TeamName,
            InviteCode = team.InviteCode,
            GamesPlayed = team.GamesPlayed,
            Points = team.Points,
            BonusPoints = team.BonusPoints,
            MaxGames = team.MaxGames,
            IsComplete = team.IsComplete,
            RegisteredAt = team.RegisteredAt,
            CompletedAt = team.CompletedAt,
            Phase = team.Phase,
            IsEliminated = team.IsEliminated,
            AdvancedToNextPhase = team.AdvancedToNextPhase,
            PhaseEliminatedAt = team.PhaseEliminatedAt
        };
    }
}
