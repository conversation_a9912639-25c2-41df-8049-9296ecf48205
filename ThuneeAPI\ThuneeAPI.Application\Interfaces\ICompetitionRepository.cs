using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Application.Interfaces;

/// <summary>
/// Repository interface for Competition entity operations
/// </summary>
public interface ICompetitionRepository
{
    /// <summary>
    /// Creates a new competition
    /// </summary>
    /// <param name="competition">Competition entity to create</param>
    /// <returns>Created competition with generated ID</returns>
    Task<Competition> CreateAsync(Competition competition);

    /// <summary>
    /// Gets a competition by its ID
    /// </summary>
    /// <param name="id">Competition ID</param>
    /// <returns>Competition entity or null if not found</returns>
    Task<Competition?> GetByIdAsync(Guid id);

    /// <summary>
    /// Gets all competitions
    /// </summary>
    /// <returns>Collection of competitions</returns>
    Task<IEnumerable<Competition>> GetAllAsync();

    /// <summary>
    /// Gets competitions by status
    /// </summary>
    /// <param name="status">Competition status</param>
    /// <returns>Collection of competitions</returns>
    Task<IEnumerable<Competition>> GetByStatusAsync(string status);

    /// <summary>
    /// Updates a competition entity
    /// </summary>
    /// <param name="competition">Competition entity to update</param>
    /// <returns>Number of affected rows</returns>
    Task<int> UpdateAsync(Competition competition);

    /// <summary>
    /// Deletes a competition
    /// </summary>
    /// <param name="id">Competition ID</param>
    /// <returns>Task representing the operation</returns>
    Task DeleteAsync(Guid id);

    /// <summary>
    /// Gets public competitions
    /// </summary>
    /// <returns>Collection of public competitions</returns>
    Task<IEnumerable<Competition>> GetPublicCompetitionsAsync();

    /// <summary>
    /// Gets upcoming competitions
    /// </summary>
    /// <returns>Collection of upcoming competitions</returns>
    Task<IEnumerable<Competition>> GetUpcomingCompetitionsAsync();

    /// <summary>
    /// Gets active competitions
    /// </summary>
    /// <returns>Collection of active competitions</returns>
    Task<IEnumerable<Competition>> GetActiveCompetitionsAsync();

    /// <summary>
    /// Executes a stored procedure without returning results
    /// </summary>
    /// <param name="storedProcedureName">Name of the stored procedure</param>
    /// <param name="parameters">Parameters for the stored procedure</param>
    /// <returns>Number of affected rows</returns>
    Task<int> ExecuteStoredProcedureAsync(string storedProcedureName, object? parameters = null);

    /// <summary>
    /// Executes a stored procedure and returns results
    /// </summary>
    /// <typeparam name="T">Type of the result</typeparam>
    /// <param name="storedProcedureName">Name of the stored procedure</param>
    /// <param name="parameters">Parameters for the stored procedure</param>
    /// <returns>Collection of results</returns>
    Task<IEnumerable<T>> ExecuteStoredProcedureAsync<T>(string storedProcedureName, object? parameters = null);
}
