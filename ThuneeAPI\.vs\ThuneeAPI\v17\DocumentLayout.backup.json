{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.infrastructure\\data\\repositories\\competitionrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj|solutionrelative:thuneeapi.infrastructure\\data\\repositories\\competitionrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi\\controllers\\gamescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|solutionrelative:th<PERSON><PERSON><PERSON>\\controllers\\gamescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.infrastructure\\services\\gameservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj|solutionrelative:thuneeapi.infrastructure\\services\\gameservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.application\\interfaces\\igameservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|solutionrelative:thuneeapi.application\\interfaces\\igameservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.application\\interfaces\\igamerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|solutionrelative:thuneeapi.application\\interfaces\\igamerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|solutionrelative:thuneea<PERSON>\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|solutionrelative:thuneeapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{1B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Core\\ThuneeAPI.Core.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.core\\thuneeapi.core.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{1B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Core\\ThuneeAPI.Core.csproj|solutionrelative:thuneeapi.core\\thuneeapi.core.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.application\\thuneeapi.application.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|solutionrelative:thuneeapi.application\\thuneeapi.application.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi\\controllers\\leaderboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|solutionrelative:th<PERSON><PERSON><PERSON>\\controllers\\leaderboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|solutionrelative:th<PERSON><PERSON><PERSON>\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "CompetitionRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\Data\\Repositories\\CompetitionRepository.cs", "RelativeDocumentMoniker": "ThuneeAPI.Infrastructure\\Data\\Repositories\\CompetitionRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\Data\\Repositories\\CompetitionRepository.cs", "RelativeToolTip": "ThuneeAPI.Infrastructure\\Data\\Repositories\\CompetitionRepository.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAqwBMAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T11:29:24.685Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "GameService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\Services\\GameService.cs", "RelativeDocumentMoniker": "ThuneeAPI.Infrastructure\\Services\\GameService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\Services\\GameService.cs", "RelativeToolTip": "ThuneeAPI.Infrastructure\\Services\\GameService.cs", "ViewState": "AgIAAFsAAAAAAAAAAADwv2YAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T12:19:56.722Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "IGameService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\Interfaces\\IGameService.cs", "RelativeDocumentMoniker": "ThuneeAPI.Application\\Interfaces\\IGameService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\Interfaces\\IGameService.cs", "RelativeToolTip": "ThuneeAPI.Application\\Interfaces\\IGameService.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAuwAoAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T10:49:40.76Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "IGameRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\Interfaces\\IGameRepository.cs", "RelativeDocumentMoniker": "ThuneeAPI.Application\\Interfaces\\IGameRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\Interfaces\\IGameRepository.cs", "RelativeToolTip": "ThuneeAPI.Application\\Interfaces\\IGameRepository.cs", "ViewState": "AgIAACYAAAAAAAAAAAAUwDcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T10:49:39.78Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\appsettings.Development.json", "RelativeDocumentMoniker": "ThuneeAPI\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\appsettings.Development.json", "RelativeToolTip": "ThuneeAPI\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-01T10:28:51.278Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\appsettings.json", "RelativeDocumentMoniker": "ThuneeAPI\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\appsettings.json", "RelativeToolTip": "ThuneeAPI\\appsettings.json", "ViewState": "AgIAAAcAAAAAAAAAAABhwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-01T10:28:46.136Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "GamesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\GamesController.cs", "RelativeDocumentMoniker": "ThuneeAPI\\Controllers\\GamesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\GamesController.cs", "RelativeToolTip": "ThuneeAPI\\Controllers\\GamesController.cs", "ViewState": "AgIAAAQBAAAAAAAAAAAqwBIBAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T10:28:44.745Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "ThuneeAPI.Core.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\ThuneeAPI.Core.csproj", "RelativeDocumentMoniker": "ThuneeAPI.Core\\ThuneeAPI.Core.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\ThuneeAPI.Core.csproj", "RelativeToolTip": "ThuneeAPI.Core\\ThuneeAPI.Core.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-09T12:09:17.406Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "ThuneeAPI.Application.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\ThuneeAPI.Application.csproj", "RelativeDocumentMoniker": "ThuneeAPI.Application\\ThuneeAPI.Application.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\ThuneeAPI.Application.csproj", "RelativeToolTip": "ThuneeAPI.Application\\ThuneeAPI.Application.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-09T12:09:16.264Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "LeaderboardController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\LeaderboardController.cs", "RelativeDocumentMoniker": "ThuneeAPI\\Controllers\\LeaderboardController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\LeaderboardController.cs", "RelativeToolTip": "ThuneeAPI\\Controllers\\LeaderboardController.cs", "ViewState": "AgIAAB4AAAAAAAAAAAA6wCYAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T12:08:45.638Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "AuthController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "ThuneeAPI\\Controllers\\AuthController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\AuthController.cs", "RelativeToolTip": "ThuneeAPI\\Controllers\\AuthController.cs", "ViewState": "AgIAAPwAAAAAAAAAAAAmwP0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T11:21:47.418Z", "EditorCaption": ""}]}]}]}