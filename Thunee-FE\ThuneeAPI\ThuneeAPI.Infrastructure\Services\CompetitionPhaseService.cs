using Microsoft.Extensions.Logging;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;
using ThuneeAPI.Infrastructure.Data.Repositories;

namespace ThuneeAPI.Infrastructure.Services;

public class CompetitionPhaseService : ICompetitionPhaseService
{
    private readonly ILogger<CompetitionPhaseService> _logger;
    private readonly ICompetitionRepository _competitionRepository;
    private readonly ICompetitionTeamRepository _competitionTeamRepository;
    private readonly IUserRepository _userRepository;
    private readonly IEmailService _emailService;

    public CompetitionPhaseService(
        ILogger<CompetitionPhaseService> logger,
        ICompetitionRepository competitionRepository,
        ICompetitionTeamRepository competitionTeamRepository,
        IUserRepository userRepository,
        IEmailService emailService)
    {
        _logger = logger;
        _competitionRepository = competitionRepository;
        _competitionTeamRepository = competitionTeamRepository;
        _userRepository = userRepository;
        _emailService = emailService;
    }

    public async Task<CompetitionDto> AdvanceCompetitionPhaseAsync(Guid competitionId, string newPhase)
    {
        _logger.LogInformation("Advancing competition {CompetitionId} to phase {Phase}", competitionId, newPhase);

        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        // Get eligible teams for the new phase (from the previous phase)
        var previousPhase = GetPreviousPhase(newPhase);
        var allTeams = await _competitionTeamRepository.GetByCompetitionIdAsync(competitionId);
        var eligibleTeams = allTeams.Where(t => t.Phase == previousPhase && !t.IsEliminated).ToList();

        // Sort teams by total points (points + bonus points) descending
        var sortedTeams = eligibleTeams
            .OrderByDescending(t => t.Points + t.BonusPoints)
            .ThenByDescending(t => t.Points)
            .ThenBy(t => t.TeamName)
            .ToList();

        // Determine how many teams advance based on new phase
        var advancingCount = newPhase switch
        {
            "Top32" => 32,  // Top 32 teams from Leaderboard
            "Top16" => 16,  // Top 16 teams from Top32
            "Top8" => 8,    // Top 8 teams from Top16
            "Top4" => 4,    // Top 4 teams from Top8
            "Final" => 2,   // Top 2 teams from Top4
            _ => 0
        };

        var teamsToAdvance = sortedTeams.Take(advancingCount).ToList();
        var teamsToEliminate = sortedTeams.Skip(advancingCount).ToList();

        _logger.LogInformation("Advancing {AdvanceCount} teams to {NewPhase}, eliminating {EliminateCount} teams",
            teamsToAdvance.Count, newPhase, teamsToEliminate.Count);

        // Advance eligible teams to the new phase
        foreach (var team in teamsToAdvance)
        {
            team.Phase = newPhase;
            team.AdvancedToNextPhase = true;
            await _competitionTeamRepository.UpdateAsync(team);
        }

        // Eliminate teams that are not eligible
        foreach (var team in teamsToEliminate)
        {
            team.IsEliminated = true;
            team.PhaseEliminatedAt = DateTime.UtcNow;
            await _competitionTeamRepository.UpdateAsync(team);
        }

        // Update the competition phase
        competition.Phase = newPhase;
        competition.UpdatedAt = DateTime.UtcNow;

        // Set phase end date based on phase type
        competition.PhaseEndDate = newPhase switch
        {
            "Top32" => DateTime.UtcNow.AddDays(7), // 1 week for Top32
            "Top16" => DateTime.UtcNow.AddDays(3), // 3 days for knockout phases
            "Top8" => DateTime.UtcNow.AddDays(2),
            "Top4" => DateTime.UtcNow.AddDays(1),
            "Final" => DateTime.UtcNow.AddHours(12),
            _ => null
        };

        // Set max games per phase
        competition.MaxGamesPerPhase = newPhase switch
        {
            "Top32" => 10, // Max 10 games in Top32 phase
            "Top16" => 1,  // Single knockout game
            "Top8" => 1,   // Single knockout game
            "Top4" => 1,   // Single knockout game
            "Final" => 1,  // Single knockout game
            _ => null
        };

        await _competitionRepository.UpdateAsync(competition);

        _logger.LogInformation("Successfully advanced competition {CompetitionId} to phase {Phase} with {AdvancedCount} teams advanced and {EliminatedCount} teams eliminated",
            competitionId, newPhase, teamsToAdvance.Count, teamsToEliminate.Count);

        return MapToCompetitionDto(competition);
    }

    public async Task<List<CompetitionTeamDto>> GetTeamsForPhaseAsync(Guid competitionId, string phase)
    {
        _logger.LogInformation("Getting teams for competition {CompetitionId} phase {Phase}", competitionId, phase);
        
        var teams = await _competitionTeamRepository.GetByCompetitionIdAsync(competitionId);
        var phaseTeams = teams.Where(t => t.Phase == phase && !t.IsEliminated).ToList();
        
        var teamDtos = new List<CompetitionTeamDto>();
        
        foreach (var team in phaseTeams)
        {
            var player1 = await _userRepository.GetByIdAsync(team.Player1Id);
            var player2 = team.Player2Id.HasValue ? await _userRepository.GetByIdAsync(team.Player2Id.Value) : null;
            
            teamDtos.Add(MapToCompetitionTeamDto(team, player1!, player2));
        }
        
        return teamDtos;
    }

    public async Task<List<CompetitionTeamDto>> GetEligibleTeamsForNextPhaseAsync(Guid competitionId)
    {
        _logger.LogInformation("Getting eligible teams for next phase in competition {CompetitionId}", competitionId);
        
        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        var teams = await GetTeamsForPhaseAsync(competitionId, competition.Phase);
        
        // Sort by total points (points + bonus points) descending
        var sortedTeams = teams.OrderByDescending(t => t.Points + t.BonusPoints)
                              .ThenByDescending(t => t.Points)
                              .ThenBy(t => t.GamesPlayed)
                              .ToList();

        // Determine how many teams advance based on current phase
        var advancingCount = competition.Phase switch
        {
            "Leaderboard" => 32,
            "Top32" => 16,
            "Top16" => 8,
            "Top8" => 4,
            "Top4" => 2,
            _ => 0
        };

        return sortedTeams.Take(advancingCount).ToList();
    }

    public async Task EliminateTeamsAsync(Guid competitionId, List<Guid> teamIds)
    {
        _logger.LogInformation("Eliminating {Count} teams from competition {CompetitionId}", teamIds.Count, competitionId);
        
        foreach (var teamId in teamIds)
        {
            var team = await _competitionTeamRepository.GetByIdAsync(teamId);
            if (team != null)
            {
                team.IsEliminated = true;
                team.PhaseEliminatedAt = DateTime.UtcNow;
                await _competitionTeamRepository.UpdateAsync(team);
            }
        }
    }

    public async Task AdvanceTeamsToNextPhaseAsync(Guid competitionId, List<Guid> teamIds)
    {
        _logger.LogInformation("Advancing {Count} teams to next phase in competition {CompetitionId}", teamIds.Count, competitionId);
        
        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        var nextPhase = GetNextPhase(competition.Phase);
        
        foreach (var teamId in teamIds)
        {
            var team = await _competitionTeamRepository.GetByIdAsync(teamId);
            if (team != null)
            {
                team.Phase = nextPhase;
                team.AdvancedToNextPhase = true;
                await _competitionTeamRepository.UpdateAsync(team);
            }
        }
    }

    public async Task<CompetitionPhaseLobbyDto> CreatePhaseLobbyAsync(CreateCompetitionPhaseLobbyDto createDto, Guid adminId)
    {
        _logger.LogInformation("Creating phase lobby for competition {CompetitionId} phase {Phase}", createDto.CompetitionId, createDto.Phase);

        // Get competition details
        var competition = await _competitionRepository.GetByIdAsync(createDto.CompetitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        // Get teams for the lobby
        var teams = new List<CompetitionTeamDto>();
        foreach (var teamId in createDto.TeamIds)
        {
            var team = await _competitionTeamRepository.GetByIdAsync(teamId);
            if (team != null)
            {
                var player1 = await _userRepository.GetByIdAsync(team.Player1Id);
                var player2 = team.Player2Id.HasValue ? await _userRepository.GetByIdAsync(team.Player2Id.Value) : null;
                teams.Add(MapToCompetitionTeamDto(team, player1!, player2));
            }
        }

        if (teams.Count != 2)
            throw new ArgumentException("Exactly 2 teams are required for a knockout lobby");

        // Generate lobby code
        var lobbyCode = GenerateLobbyCode();

        // Create the lobby (this would use a repository in full implementation)
        var lobby = new CompetitionPhaseLobbyDto
        {
            Id = Guid.NewGuid(),
            CompetitionId = createDto.CompetitionId,
            Phase = createDto.Phase,
            LobbyCode = lobbyCode,
            CreatedByAdminId = adminId,
            CreatedByAdminUsername = "Admin", // Would get from user repository
            CreatedAt = DateTime.UtcNow,
            Teams = teams.Select(t => new CompetitionPhaseLobbyTeamDto
            {
                Id = Guid.NewGuid(),
                LobbyId = Guid.NewGuid(),
                CompetitionTeamId = t.Id,
                TeamName = t.TeamName,
                IsWinner = false
            }).ToList()
        };

        // Send emails to all players in both teams
        await SendKnockoutLobbyEmails(teams, competition.Name, createDto.Phase, lobbyCode);

        return lobby;
    }

    private async Task SendKnockoutLobbyEmails(List<CompetitionTeamDto> teams, string competitionName, string phase, string lobbyCode)
    {
        _logger.LogInformation("Sending knockout lobby emails to {TeamCount} teams", teams.Count);

        // Send emails in background to avoid blocking
        _ = Task.Run(async () =>
        {
            try
            {
                var team1 = teams[0];
                var team2 = teams[1];

                // Send to Team 1 players
                await _emailService.SendKnockoutLobbyEmailAsync(
                    team1.Player1.Username + "@example.com", // Would use actual email from user
                    team1.Player1.Username,
                    team1.TeamName,
                    competitionName,
                    phase,
                    lobbyCode,
                    team2.TeamName
                );

                if (team1.Player2 != null)
                {
                    await _emailService.SendKnockoutLobbyEmailAsync(
                        team1.Player2.Username + "@example.com", // Would use actual email from user
                        team1.Player2.Username,
                        team1.TeamName,
                        competitionName,
                        phase,
                        lobbyCode,
                        team2.TeamName
                    );
                }

                // Send to Team 2 players
                await _emailService.SendKnockoutLobbyEmailAsync(
                    team2.Player1.Username + "@example.com", // Would use actual email from user
                    team2.Player1.Username,
                    team2.TeamName,
                    competitionName,
                    phase,
                    lobbyCode,
                    team1.TeamName
                );

                if (team2.Player2 != null)
                {
                    await _emailService.SendKnockoutLobbyEmailAsync(
                        team2.Player2.Username + "@example.com", // Would use actual email from user
                        team2.Player2.Username,
                        team2.TeamName,
                        competitionName,
                        phase,
                        lobbyCode,
                        team1.TeamName
                    );
                }

                _logger.LogInformation("Knockout lobby emails sent successfully for lobby {LobbyCode}", lobbyCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send knockout lobby emails for lobby {LobbyCode}", lobbyCode);
            }
        });
    }

    private string GenerateLobbyCode()
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, 8)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    public async Task<List<CompetitionPhaseLobbyDto>> GetPhaseLobbiesAsync(Guid competitionId, string phase)
    {
        _logger.LogInformation("Getting phase lobbies for competition {CompetitionId} phase {Phase}", competitionId, phase);
        
        // This would need a repository implementation
        throw new NotImplementedException("Phase lobby retrieval requires database implementation");
    }

    public async Task<CompetitionPhaseLobbyDto> GetPhaseLobbyByCodeAsync(string lobbyCode)
    {
        _logger.LogInformation("Getting phase lobby by code {LobbyCode}", lobbyCode);
        
        // This would need a repository implementation
        throw new NotImplementedException("Phase lobby retrieval requires database implementation");
    }

    public async Task DeletePhaseLobbyAsync(Guid lobbyId)
    {
        _logger.LogInformation("Deleting phase lobby {LobbyId}", lobbyId);
        
        // This would need a repository implementation
        throw new NotImplementedException("Phase lobby deletion requires database implementation");
    }

    public async Task SetLobbyWinnerAsync(Guid lobbyId, Guid winnerTeamId)
    {
        _logger.LogInformation("Setting winner {WinnerTeamId} for lobby {LobbyId}", winnerTeamId, lobbyId);
        
        // This would need a repository implementation
        throw new NotImplementedException("Setting lobby winner requires database implementation");
    }

    public async Task<BracketDto> GenerateBracketAsync(Guid competitionId, string phase)
    {
        _logger.LogInformation("Generating bracket for competition {CompetitionId} phase {Phase}", competitionId, phase);
        
        var teams = await GetTeamsForPhaseAsync(competitionId, phase);
        var lobbies = await GetPhaseLobbiesAsync(competitionId, phase);
        
        var matches = new List<BracketMatchDto>();
        
        // Generate bracket matches based on teams and lobbies
        // This is a simplified implementation
        for (int i = 0; i < teams.Count; i += 2)
        {
            var team1 = teams[i];
            var team2 = i + 1 < teams.Count ? teams[i + 1] : null;
            
            matches.Add(new BracketMatchDto
            {
                LobbyId = Guid.NewGuid(), // Would be actual lobby ID
                LobbyCode = $"LOBBY{i/2 + 1:D3}",
                Team1 = team1,
                Team2 = team2,
                IsCompleted = false
            });
        }
        
        return new BracketDto
        {
            Phase = phase,
            Matches = matches
        };
    }

    public async Task<List<BracketDto>> GetCompetitionBracketsAsync(Guid competitionId)
    {
        _logger.LogInformation("Getting all brackets for competition {CompetitionId}", competitionId);
        
        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        var brackets = new List<BracketDto>();
        var phases = new[] { "Top16", "Top8", "Top4", "Final" };
        
        foreach (var phase in phases)
        {
            if (ShouldIncludePhase(competition.Phase, phase))
            {
                var bracket = await GenerateBracketAsync(competitionId, phase);
                brackets.Add(bracket);
            }
        }
        
        return brackets;
    }

    public async Task<CompetitionTeamPhaseStatsDto> CreatePhaseStatsAsync(Guid competitionTeamId, string phase)
    {
        _logger.LogInformation("Creating phase stats for team {TeamId} phase {Phase}", competitionTeamId, phase);
        
        // This would need a repository implementation
        throw new NotImplementedException("Phase stats creation requires database implementation");
    }

    public async Task<CompetitionTeamPhaseStatsDto> UpdatePhaseStatsAsync(Guid competitionTeamId, string phase, int points, int bonusPoints, int ballsWon)
    {
        _logger.LogInformation("Updating phase stats for team {TeamId} phase {Phase}", competitionTeamId, phase);
        
        // This would need a repository implementation
        throw new NotImplementedException("Phase stats update requires database implementation");
    }

    public async Task<List<CompetitionTeamPhaseStatsDto>> GetTeamPhaseStatsAsync(Guid competitionTeamId)
    {
        _logger.LogInformation("Getting phase stats for team {TeamId}", competitionTeamId);
        
        // This would need a repository implementation
        throw new NotImplementedException("Phase stats retrieval requires database implementation");
    }

    public async Task ProcessPhaseEndAsync(Guid competitionId)
    {
        _logger.LogInformation("Processing phase end for competition {CompetitionId}", competitionId);

        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        // Get eligible teams for next phase
        var eligibleTeams = await GetEligibleTeamsForNextPhaseAsync(competitionId);
        var eligibleTeamIds = eligibleTeams.Select(t => t.Id).ToList();

        // Get all teams in current phase
        var allTeams = await GetTeamsForPhaseAsync(competitionId, competition.Phase);
        var eliminatedTeams = allTeams.Where(t => !eligibleTeamIds.Contains(t.Id)).ToList();
        var eliminatedTeamIds = eliminatedTeams.Select(t => t.Id).ToList();

        // Eliminate non-advancing teams
        await EliminateTeamsAsync(competitionId, eliminatedTeamIds);

        // Advance eligible teams
        await AdvanceTeamsToNextPhaseAsync(competitionId, eligibleTeamIds);

        // Advance competition to next phase
        var nextPhase = GetNextPhase(competition.Phase);
        await AdvanceCompetitionPhaseAsync(competitionId, nextPhase);

        // Send phase advancement/elimination emails
        await SendPhaseAdvancementEmails(eligibleTeams, eliminatedTeams, competition.Name, nextPhase);
    }

    private async Task SendPhaseAdvancementEmails(List<CompetitionTeamDto> advancedTeams, List<CompetitionTeamDto> eliminatedTeams, string competitionName, string nextPhase)
    {
        _logger.LogInformation("Sending phase advancement emails to {AdvancedCount} advanced teams and {EliminatedCount} eliminated teams",
            advancedTeams.Count, eliminatedTeams.Count);

        // Send emails in background to avoid blocking
        _ = Task.Run(async () =>
        {
            try
            {
                // Send advancement emails
                foreach (var team in advancedTeams)
                {
                    // Send to player 1
                    await _emailService.SendPhaseAdvancementEmailAsync(
                        team.Player1.Username + "@example.com", // Would use actual email from user
                        team.Player1.Username,
                        team.TeamName,
                        competitionName,
                        nextPhase,
                        false // not eliminated
                    );

                    // Send to player 2 if exists
                    if (team.Player2 != null)
                    {
                        await _emailService.SendPhaseAdvancementEmailAsync(
                            team.Player2.Username + "@example.com", // Would use actual email from user
                            team.Player2.Username,
                            team.TeamName,
                            competitionName,
                            nextPhase,
                            false // not eliminated
                        );
                    }
                }

                // Send elimination emails
                foreach (var team in eliminatedTeams)
                {
                    // Send to player 1
                    await _emailService.SendPhaseAdvancementEmailAsync(
                        team.Player1.Username + "@example.com", // Would use actual email from user
                        team.Player1.Username,
                        team.TeamName,
                        competitionName,
                        nextPhase,
                        true // eliminated
                    );

                    // Send to player 2 if exists
                    if (team.Player2 != null)
                    {
                        await _emailService.SendPhaseAdvancementEmailAsync(
                            team.Player2.Username + "@example.com", // Would use actual email from user
                            team.Player2.Username,
                            team.TeamName,
                            competitionName,
                            nextPhase,
                            true // eliminated
                        );
                    }
                }

                _logger.LogInformation("Phase advancement emails sent successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send phase advancement emails");
            }
        });
    }

    public async Task<bool> CanAdvanceToNextPhaseAsync(Guid competitionId)
    {
        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            return false;

        // Check if phase end date has passed
        if (competition.PhaseEndDate.HasValue && DateTime.UtcNow >= competition.PhaseEndDate.Value)
            return true;

        // For knockout phases, check if all matches are completed
        if (IsKnockoutPhase(competition.Phase))
        {
            var lobbies = await GetPhaseLobbiesAsync(competitionId, competition.Phase);
            return lobbies.All(l => l.Teams.Any(t => t.IsWinner));
        }

        return false;
    }

    public async Task<List<CompetitionTeamDto>> CalculatePhaseRankingsAsync(Guid competitionId, string phase)
    {
        _logger.LogInformation("Calculating phase rankings for competition {CompetitionId} phase {Phase}", competitionId, phase);
        
        var teams = await GetTeamsForPhaseAsync(competitionId, phase);
        
        // Sort by total points, then by points, then by games played (ascending)
        var rankedTeams = teams.OrderByDescending(t => t.Points + t.BonusPoints)
                              .ThenByDescending(t => t.Points)
                              .ThenBy(t => t.GamesPlayed)
                              .ToList();

        // Assign ranks
        for (int i = 0; i < rankedTeams.Count; i++)
        {
            // Rank would be stored in a separate ranking system
            // For now, we just return the sorted list
        }

        return rankedTeams;
    }

    // Helper methods
    private static string GetNextPhase(string currentPhase)
    {
        return currentPhase switch
        {
            "Leaderboard" => "Top32",
            "Top32" => "Top16",
            "Top16" => "Top8",
            "Top8" => "Top4",
            "Top4" => "Final",
            "Final" => "Completed",
            _ => currentPhase
        };
    }

    private static string GetPreviousPhase(string currentPhase)
    {
        return currentPhase switch
        {
            "Top32" => "Leaderboard",
            "Top16" => "Top32",
            "Top8" => "Top16",
            "Top4" => "Top8",
            "Final" => "Top4",
            "Completed" => "Final",
            _ => "Leaderboard"
        };
    }

    private static bool IsKnockoutPhase(string phase)
    {
        return phase is "Top16" or "Top8" or "Top4" or "Final";
    }

    private static bool ShouldIncludePhase(string currentPhase, string targetPhase)
    {
        var phaseOrder = new[] { "Leaderboard", "Top32", "Top16", "Top8", "Top4", "Final" };
        var currentIndex = Array.IndexOf(phaseOrder, currentPhase);
        var targetIndex = Array.IndexOf(phaseOrder, targetPhase);
        
        return targetIndex >= currentIndex;
    }

    private static CompetitionDto MapToCompetitionDto(Competition competition)
    {
        return new CompetitionDto
        {
            Id = competition.Id,
            Name = competition.Name,
            Description = competition.Description,
            StartDate = competition.StartDate,
            EndDate = competition.EndDate,
            Status = competition.Status,
            MaxTeams = competition.MaxTeams,
            CurrentTeams = competition.CurrentTeams,
            EntryFee = competition.EntryFee,
            Prizes = new PrizesDto
            {
                First = competition.PrizeFirst,
                Second = competition.PrizeSecond,
                Third = competition.PrizeThird
            },
            TotalPrizePool = competition.TotalPrizePool,
            Rules = competition.Rules,
            IsPublic = competition.IsPublic,
            AllowSpectators = competition.AllowSpectators,
            CreatedAt = competition.CreatedAt,
            Phase = competition.Phase,
            PhaseEndDate = competition.PhaseEndDate,
            MaxGamesPerPhase = competition.MaxGamesPerPhase
        };
    }

    private static CompetitionTeamDto MapToCompetitionTeamDto(CompetitionTeam team, User player1, User? player2)
    {
        return new CompetitionTeamDto
        {
            Id = team.Id,
            TeamName = team.TeamName,
            Player1 = new PlayerDto { Id = player1.Id, Username = player1.Username },
            Player2 = player2 != null ? new PlayerDto { Id = player2.Id, Username = player2.Username } : null,
            InviteCode = team.InviteCode,
            GamesPlayed = team.GamesPlayed,
            Points = team.Points,
            BonusPoints = team.BonusPoints,
            MaxGames = team.MaxGames,
            IsComplete = team.IsComplete,
            RegisteredAt = team.RegisteredAt,
            CompletedAt = team.CompletedAt,
            Phase = team.Phase,
            IsEliminated = team.IsEliminated,
            AdvancedToNextPhase = team.AdvancedToNextPhase,
            PhaseEliminatedAt = team.PhaseEliminatedAt
        };
    }
}
